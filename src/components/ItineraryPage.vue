<template>
  <section class="min-h-screen w-full bg-rose-50 flex items-center justify-center p-6 relative">
    <FallingHearts
      :count="50"
      :min-size="25"
      :max-size="45"
      :speed="0.8"
      :opacity="0.7"
    />
    <div class="bg-white max-w-2xl w-full rounded-2xl shadow-md p-8 sm:p-10 relative z-10">
      <!-- Header -->
      <header class="text-center mb-8">
        <h1 class="text-4xl font-serif text-neutral-900">
          {{ props.title }}
        </h1>
        <p class="text-sm text-neutral-500 uppercase tracking-wide">
          {{ props.date }}
        </p>
      </header>

      <!-- Schedule Items -->
      <ul class="space-y-6">
        <ItineraryItem
          v-for="(item, idx) in itineraryItems"
          :key="idx"
          :time="item.time"
          :title="item.title"
          :description="item.description"
        />
      </ul>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import ItineraryItem from './ItineraryItem.vue'
import FallingHearts from './FallingHearts.vue'

interface ItineraryItemData {
  time: string
  title: string
  description: string
}

interface ItineraryPageProps {
  title?: string
  date?: string
  items?: ItineraryItemData[]
}

const props = withDefaults(defineProps<ItineraryPageProps>(), {
  title: 'Event Schedule',
  date: 'Today',
  items: () => [],
})

const defaultItems: ItineraryItemData[] = [
  {
    time: '9:00 AM',
    title: 'Registration & Welcome',
    description: 'Check-in, welcome coffee, and networking with fellow attendees.',
  },
  {
    time: '9:30 AM',
    title: 'Opening Keynote',
    description: 'Welcome address and overview of the day\'s agenda.',
  },
  {
    time: '10:15 AM',
    title: 'Workshop Session A',
    description: 'Interactive workshop covering fundamental concepts and best practices.',
  },
  {
    time: '11:30 AM',
    title: 'Coffee Break',
    description: 'Refreshments and informal networking opportunity.',
  },
  {
    time: '12:00 PM',
    title: 'Panel Discussion',
    description: 'Expert panel discussing industry trends and future outlook.',
  },
  {
    time: '1:00 PM',
    title: 'Lunch Break',
    description: 'Catered lunch with continued networking opportunities.',
  },
  {
    time: '2:30 PM',
    title: 'Closing Remarks',
    description: 'Summary of key takeaways and next steps.',
  },
]

const itineraryItems = computed(() =>
  props.items.length > 0 ? props.items : defaultItems
)

</script>
