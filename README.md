# Event Itinerary with Falling Hearts

A beautiful Vue 3 application featuring an elegant event itinerary page with animated falling hearts background.

## Features

- 🎨 **Beautiful UI**: Clean, modern design with Tailwind CSS
- 💖 **Animated Hearts**: Beautiful canvas-based falling hearts animation with parallax effects
- 📅 **Event Itinerary**: Responsive itinerary display with time, title, and descriptions
- ⚡ **Performance**: Optimized canvas animations with proper device pixel ratio handling
- 🎛️ **Configurable**: Customizable heart animation parameters
- 📱 **Responsive**: Works beautifully on all device sizes

## Tech Stack

- **Vue 3** with Composition API and TypeScript
- **Tailwind CSS** for styling
- **Vite** for fast development and building
- **Canvas 2D API** for the falling hearts effect

## Architecture

The project features a clean, modular architecture:

```
src/
├── components/
│   ├── FallingHearts.vue       # Heart animation component (CSS-based)
│   ├── ItineraryPage.vue       # Main page component
│   └── ItineraryItem.vue       # Individual itinerary item
├── App.vue                     # Root component
├── main.ts                     # Application entry point
└── style.css                   # Global styles
```

## Project Setup

```sh
yarn
```

### Compile and Hot-Reload for Development

```sh
yarn dev
```

### Type-Check, Compile and Minify for Production

```sh
yarn build
```

### Lint with [ESLint](https://eslint.org/)

```sh
yarn lint
```
