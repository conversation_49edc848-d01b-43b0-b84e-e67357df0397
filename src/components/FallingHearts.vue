<template>
  <canvas ref="canvas" class="hearts-canvas" />
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'

/**
 * Falling Hearts Canvas Component
 *
 * A beautiful animated background of falling hearts with:
 * - Depth-based parallax effects
 * - Smooth animations with proper device pixel ratio handling
 * - Performance optimizations (pause when hidden)
 * - Fully reactive to prop changes
 * - Larger, more prominent hearts for better visual impact
 */

interface Heart {
  x: number
  y: number
  z: number // depth (0..1) for parallax
  size: number
  vy: number
  vx: number
  angle: number
  spin: number
  swayAmp: number
  swayFreq: number
}

interface HeartAnimationProps {
  count?: number
  minSize?: number
  maxSize?: number
  speed?: number
  wind?: number
  opacity?: number
  pauseWhenHidden?: boolean
}

const props = withDefaults(defineProps<HeartAnimationProps>(), {
  count: 60,
  minSize: 20,
  maxSize: 45,
  speed: 0.8,
  wind: 0.3,
  opacity: 0.85,
  pauseWhenHidden: true,
})

// Canvas and animation state
const canvas = ref<HTMLCanvasElement | null>(null)
let ctx: CanvasRenderingContext2D | null = null
let animationId = 0
let hearts: Heart[] = []

// Constants
const TIME_SCALE = 0.001
const ROTATION_OSC = 0.18
const SWAY_SCALE = 0.18

// Computed properties
const devicePixelRatio = computed(() =>
  Math.max(1, Math.min(2, window.devicePixelRatio || 1))
)

// Utility functions
const random = (min: number, max: number): number =>
  Math.random() * (max - min) + min

const resizeCanvas = (): void => {
  if (!canvas.value) return

  const { clientWidth, clientHeight } = canvas.value
  const scale = devicePixelRatio.value

  canvas.value.width = Math.floor(clientWidth * scale)
  canvas.value.height = Math.floor(clientHeight * scale)
  canvas.value.style.width = `${clientWidth}px`
  canvas.value.style.height = `${clientHeight}px`

  if (ctx) {
    ctx.scale(scale, scale)
  }
}

const createHeart = (width: number, height: number): Heart => {
  // Bias toward background (smaller z values more common)
  const z = Math.random() ** 2
  const depthFactor = 0.5 + 0.5 * (1 - z)

  const size = random(props.minSize, props.maxSize) * depthFactor
  const vy = props.speed * random(0.6, 1.4) * depthFactor
  const vx = props.wind * random(0.2, 1.0) * depthFactor

  return {
    x: Math.random() * width,
    y: random(-height, 0),
    z,
    size,
    vy,
    vx: Math.random() < 0.5 ? -vx : vx,
    angle: Math.random() * Math.PI * 2,
    spin: random(-0.02, 0.02),
    swayAmp: random(0.5, 2.5),
    swayFreq: random(0.6, 1.4),
  }
}

const drawHeart = (ctx: CanvasRenderingContext2D, size: number, baseAlpha: number): void => {
  // Create gradient that varies with depth
  const grad = ctx.createRadialGradient(0, -size * 0.2, 0, 0, 0, size)
  grad.addColorStop(0, `rgba(255, 210, 225, ${baseAlpha})`)
  grad.addColorStop(1, `rgba(255, 120, 160, ${Math.max(0, baseAlpha * 0.9)})`)

  ctx.fillStyle = grad
  ctx.strokeStyle = `rgba(220, 100, 150, ${baseAlpha * 0.25})`
  ctx.lineWidth = Math.max(0.5, 0.8)

  // Draw heart path
  ctx.beginPath()
  ctx.moveTo(0, size * 0.35)

  // Right curve
  ctx.bezierCurveTo(
    size * 0.9, -size * 0.35,
    size * 0.55, -size * 0.95,
    0, -size * 0.55
  )

  // Left curve
  ctx.bezierCurveTo(
    -size * 0.55, -size * 0.95,
    -size * 0.9, -size * 0.35,
    0, size * 0.35
  )

  ctx.closePath()
  ctx.fill()
  ctx.stroke()
}

const renderHeart = (heart: Heart, timestamp: number): void => {
  if (!ctx || !canvas.value) return

  ctx.save()
  ctx.translate(heart.x, heart.y)

  // Apply horizontal sway with parallax effect
  const sway = Math.sin(
    timestamp * TIME_SCALE * heart.swayFreq + heart.angle
  ) * heart.swayAmp * (1 - heart.z)
  ctx.translate(sway, 0)

  // Apply rotation with gentle oscillation
  ctx.rotate(
    heart.angle +
    Math.sin(timestamp * TIME_SCALE + heart.angle) * ROTATION_OSC
  )

  // Calculate alpha based on depth and global opacity
  const baseAlpha = props.opacity * (0.4 + 0.6 * (1 - heart.z))

  drawHeart(ctx, heart.size, baseAlpha)
  ctx.restore()
}

const updateHeart = (heart: Heart, width: number, height: number, timestamp: number): void => {
  const scale = devicePixelRatio.value

  // Update position
  heart.y += heart.vy * scale
  heart.x += (heart.vx + Math.sin(timestamp * TIME_SCALE + heart.angle) * SWAY_SCALE) * scale
  heart.angle += heart.spin

  // Handle edge wrapping
  if (heart.y - heart.size > height) {
    // Respawn at top with new properties
    Object.assign(heart, createHeart(width, height))
    heart.y = -heart.size
    heart.x = Math.random() * width
  }

  if (heart.x < -heart.size) {
    heart.x = width + heart.size
  } else if (heart.x > width + heart.size) {
    heart.x = -heart.size
  }
}

const animationLoop = (timestamp: number): void => {
  if (!ctx || !canvas.value) return

  const { clientWidth, clientHeight } = canvas.value
  const { width: canvasWidth, height: canvasHeight } = canvas.value

  ctx.clearRect(0, 0, canvasWidth, canvasHeight)

  // Update and render all hearts using client dimensions
  for (const heart of hearts) {
    updateHeart(heart, clientWidth, clientHeight, timestamp)
    renderHeart(heart, timestamp)
  }

  animationId = requestAnimationFrame(animationLoop)
}

const initializeAnimation = (): void => {
  if (!canvas.value) return

  ctx = canvas.value.getContext('2d')
  if (!ctx) return

  resizeCanvas()
  hearts = []

  const { clientWidth, clientHeight } = canvas.value

  // Create initial hearts with random vertical distribution
  for (let i = 0; i < props.count; i++) {
    const heart = createHeart(clientWidth, clientHeight)
    heart.y = Math.random() * clientHeight // Random initial spread
    hearts.push(heart)
  }

  stopAnimation()
  animationId = requestAnimationFrame(animationLoop)
}

const stopAnimation = (): void => {
  cancelAnimationFrame(animationId)
}

const handleVisibilityChange = (): void => {
  if (!props.pauseWhenHidden) return

  if (document.hidden) {
    stopAnimation()
  } else {
    initializeAnimation()
  }
}

// Lifecycle hooks
onMounted(() => {
  initializeAnimation()
  window.addEventListener('resize', resizeCanvas)
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeCanvas)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  stopAnimation()
})

// Watch for prop changes and restart animation
watch(
  () => [
    props.count,
    props.minSize,
    props.maxSize,
    props.speed,
    props.wind,
    props.opacity,
  ],
  () => {
    initializeAnimation()
  }
)
</script>

<style scoped>
.hearts-canvas {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  display: block;
  pointer-events: none; /* Allow interactions with underlying UI */

  /* Optimize rendering for smooth animations */
  will-change: transform;
  image-rendering: auto;

  /* Ensure proper layering - behind content but visible */
  z-index: 0;

  /* Smooth edges for better visual quality with larger hearts */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Performance optimization for reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .hearts-canvas {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
