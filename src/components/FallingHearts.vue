<template>
  <div ref="container" class="hearts-container">
    <!-- Debug: Show container size -->
    <div class="debug-info">{{ hearts.length }} hearts, {{ containerWidth }}x{{ containerHeight }}</div>

    <div
      v-for="heart in hearts"
      :key="heart.id"
      :ref="(el) => setHeartRef(el, heart)"
      :style="{
        position: 'absolute',
        width: heart.size + 'px',
        height: heart.size + 'px',
        pointerEvents: 'none',
        background: 'red',
        borderRadius: '50%'
      }"
      class="heart-element"
    >
      <!-- Simple red circle for testing -->
      ❤️
    </div>
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch, nextTick } from 'vue'
import { useMotion } from '@vueuse/motion'

/**
 * Falling Hearts Motion Component
 *
 * A beautiful animated background of falling hearts with:
 * - Depth-based parallax effects
 * - Smooth animations using @vueuse/motion
 * - Performance optimizations (pause when hidden)
 * - Fully reactive to prop changes
 * - Larger, more prominent hearts for better visual impact
 */

interface Heart {
  id: number
  x: number
  size: number
  svg: string
}

interface HeartAnimationProps {
  count?: number
  minSize?: number
  maxSize?: number
  speed?: number
  wind?: number
  opacity?: number
  pauseWhenHidden?: boolean
}

const props = withDefaults(defineProps<HeartAnimationProps>(), {
  count: 60,
  minSize: 20,
  maxSize: 45,
  speed: 0.8,
  wind: 0.3,
  opacity: 0.85,
  pauseWhenHidden: true,
})

// Container and animation state
const container = ref<HTMLDivElement | null>(null)
const hearts = ref<Heart[]>([])
let containerWidth = 0
let containerHeight = 0

// Set heart element reference and apply motion
const setHeartRef = (el: any, heart: Heart) => {
  if (el && el instanceof HTMLElement) {
    nextTick(() => {
      useMotion(el, {
        initial: { y: -50, x: heart.x },
        animate: { y: containerHeight + 50, x: heart.x + 50 },
        transition: { duration: 3, repeat: Infinity, ease: 'linear' }
      })
    })
  }
}

// Constants (for future enhancements)
// const TIME_SCALE = 0.001
// const ROTATION_OSC = 0.18

// Heart SVG template function
const getHeartSvg = (id: number, opacity: number): string => `
  <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <radialGradient id="heartGrad${id}" cx="50%" cy="30%" r="70%">
        <stop offset="0%" style="stop-color:rgba(255, 210, 225, ${opacity});stop-opacity:1" />
        <stop offset="100%" style="stop-color:rgba(255, 120, 160, ${opacity * 0.9});stop-opacity:1" />
      </radialGradient>
    </defs>
    <path d="M50,85 C50,85 15,55 15,35 C15,20 25,15 35,20 C40,22 45,25 50,35 C55,25 60,22 65,20 C75,15 85,20 85,35 C85,55 50,85 50,85 Z"
          fill="url(#heartGrad${id})"
          stroke="rgba(220, 100, 150, ${opacity * 0.25})"
          stroke-width="0.8"/>
  </svg>
`

// Utility functions
const random = (min: number, max: number): number =>
  Math.random() * (max - min) + min

const updateContainerSize = (): void => {
  if (!container.value) return

  const rect = container.value.getBoundingClientRect()
  containerWidth = rect.width
  containerHeight = rect.height
}

const createHeart = (width: number, _height: number, id: number): Heart => {
  const size = random(props.minSize, props.maxSize)
  const x = Math.random() * width
  const baseAlpha = props.opacity

  return {
    id,
    x,
    size,
    svg: getHeartSvg(id, baseAlpha),
  }
}



const initializeAnimation = (): void => {
  if (!container.value) return

  updateContainerSize()
  hearts.value = []

  // Create initial hearts
  for (let i = 0; i < Math.min(props.count, 10); i++) { // Limit to 10 for testing
    const heart = createHeart(containerWidth, containerHeight, i)
    hearts.value.push(heart)
  }
}

const handleResize = (): void => {
  updateContainerSize()
  // Recreate hearts with new dimensions
  initializeAnimation()
}

const handleVisibilityChange = (): void => {
  if (!props.pauseWhenHidden) return

  if (document.hidden) {
    // Motion will automatically pause animations when the component is hidden
    return
  } else {
    // Restart animations when visible
    initializeAnimation()
  }
}

// Lifecycle hooks
onMounted(() => {
  initializeAnimation()
  window.addEventListener('resize', handleResize)
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})

// Watch for prop changes and restart animation
watch(
  () => [
    props.count,
    props.minSize,
    props.maxSize,
    props.speed,
    props.wind,
    props.opacity,
  ],
  () => {
    initializeAnimation()
  }
)
</script>

<style scoped>
.hearts-container {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none; /* Allow interactions with underlying UI */

  /* Ensure proper layering - behind content but visible */
  z-index: 0;
}

.debug-info {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  pointer-events: none;
}

.heart-element {
  position: absolute;
  pointer-events: none;

  /* Optimize rendering for smooth animations */
  will-change: transform, opacity;

  /* Smooth edges for better visual quality */
  image-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.heart-element svg {
  width: 100%;
  height: 100%;
  display: block;
}

/* Performance optimization for reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .heart-element {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
