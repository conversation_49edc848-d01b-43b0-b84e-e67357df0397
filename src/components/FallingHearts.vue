<template>
  <div ref="container" class="hearts-container">
    <div
      v-for="heart in hearts"
      :key="heart.id"
      v-motion="{
        initial: heart.initial,
        animate: heart.animate
      }"
      :style="heart.style"
      class="heart-element"
      v-html="heart.svg"
    />
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'

/**
 * Falling Hearts Motion Component
 *
 * A beautiful animated background of falling hearts with:
 * - Depth-based parallax effects
 * - Smooth animations using @vueuse/motion
 * - Performance optimizations (pause when hidden)
 * - Fully reactive to prop changes
 * - Larger, more prominent hearts for better visual impact
 */

interface MotionConfig {
  x?: number | number[]
  y?: number | number[]
  rotate?: number | number[]
  opacity?: number
  scale?: number
  transition?: any // Simplified for compatibility
}

interface Heart {
  id: number
  x: number
  y: number
  z: number // depth (0..1) for parallax
  size: number
  vy: number
  vx: number
  angle: number
  spin: number
  swayAmp: number
  swayFreq: number
  svg: string
  initial: MotionConfig
  animate: MotionConfig
  style: Record<string, string | number>
}

interface HeartAnimationProps {
  count?: number
  minSize?: number
  maxSize?: number
  speed?: number
  wind?: number
  opacity?: number
  pauseWhenHidden?: boolean
}

const props = withDefaults(defineProps<HeartAnimationProps>(), {
  count: 60,
  minSize: 20,
  maxSize: 45,
  speed: 0.8,
  wind: 0.3,
  opacity: 0.85,
  pauseWhenHidden: true,
})

// Container and animation state
const container = ref<HTMLDivElement | null>(null)
const hearts = ref<Heart[]>([])
let containerWidth = 0
let containerHeight = 0

// Constants (for future enhancements)
// const TIME_SCALE = 0.001
// const ROTATION_OSC = 0.18

// Heart SVG template function
const getHeartSvg = (id: number, opacity: number): string => `
  <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <radialGradient id="heartGrad${id}" cx="50%" cy="30%" r="70%">
        <stop offset="0%" style="stop-color:rgba(255, 210, 225, ${opacity});stop-opacity:1" />
        <stop offset="100%" style="stop-color:rgba(255, 120, 160, ${opacity * 0.9});stop-opacity:1" />
      </radialGradient>
    </defs>
    <path d="M50,85 C50,85 15,55 15,35 C15,20 25,15 35,20 C40,22 45,25 50,35 C55,25 60,22 65,20 C75,15 85,20 85,35 C85,55 50,85 50,85 Z"
          fill="url(#heartGrad${id})"
          stroke="rgba(220, 100, 150, ${opacity * 0.25})"
          stroke-width="0.8"/>
  </svg>
`

// Utility functions
const random = (min: number, max: number): number =>
  Math.random() * (max - min) + min

const updateContainerSize = (): void => {
  if (!container.value) return

  const rect = container.value.getBoundingClientRect()
  containerWidth = rect.width
  containerHeight = rect.height
}

const createHeart = (width: number, height: number, id: number): Heart => {
  // Bias toward background (smaller z values more common)
  const z = Math.random() ** 2
  const depthFactor = 0.5 + 0.5 * (1 - z)

  const size = random(props.minSize, props.maxSize) * depthFactor
  const vy = props.speed * random(0.6, 1.4) * depthFactor
  const vx = props.wind * random(0.2, 1.0) * depthFactor

  const x = Math.random() * width
  const y = random(-height, 0)
  const angle = Math.random() * Math.PI * 2
  const spin = random(-0.02, 0.02)
  const swayAmp = random(0.5, 2.5)
  const swayFreq = random(0.6, 1.4)

  // Calculate alpha based on depth and global opacity
  const baseAlpha = props.opacity * (0.4 + 0.6 * (1 - z))

  // Calculate animation duration based on speed and distance
  const fallDuration = (height + size) / (vy * 60) // Convert to seconds

  return {
    id,
    x,
    y,
    z,
    size,
    vy,
    vx: Math.random() < 0.5 ? -vx : vx,
    angle,
    spin,
    swayAmp,
    swayFreq,
    svg: getHeartSvg(id, baseAlpha),
    initial: {
      x: x,
      y: y,
      rotate: angle * (180 / Math.PI),
      opacity: baseAlpha,
      scale: 1,
    },
    animate: {
      y: height + size,
      x: x + (vx * fallDuration * 30), // Simple horizontal drift
      rotate: (angle + spin * fallDuration * 60) * (180 / Math.PI), // Simple rotation
      transition: {
        duration: fallDuration,
        ease: 'linear',
        repeat: Infinity,
        repeatType: 'loop',
      },
    },
    style: {
      position: 'absolute',
      width: `${size}px`,
      height: `${size}px`,
      opacity: baseAlpha,
      pointerEvents: 'none',
      zIndex: Math.floor((1 - z) * 10),
    },
  }
}



const initializeAnimation = (): void => {
  if (!container.value) return

  updateContainerSize()
  hearts.value = []

  console.log('Initializing hearts:', props.count, 'Container size:', containerWidth, containerHeight)

  // Create initial hearts with random vertical distribution
  for (let i = 0; i < props.count; i++) {
    const heart = createHeart(containerWidth, containerHeight, i)
    heart.y = Math.random() * containerHeight // Random initial spread
    heart.initial.y = heart.y
    hearts.value.push(heart)
  }

  console.log('Created hearts:', hearts.value.length)
}

const handleResize = (): void => {
  updateContainerSize()
  // Recreate hearts with new dimensions
  initializeAnimation()
}

const handleVisibilityChange = (): void => {
  if (!props.pauseWhenHidden) return

  if (document.hidden) {
    // Motion will automatically pause animations when the component is hidden
    return
  } else {
    // Restart animations when visible
    initializeAnimation()
  }
}

// Lifecycle hooks
onMounted(() => {
  initializeAnimation()
  window.addEventListener('resize', handleResize)
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})

// Watch for prop changes and restart animation
watch(
  () => [
    props.count,
    props.minSize,
    props.maxSize,
    props.speed,
    props.wind,
    props.opacity,
  ],
  () => {
    initializeAnimation()
  }
)
</script>

<style scoped>
.hearts-container {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none; /* Allow interactions with underlying UI */

  /* Ensure proper layering - behind content but visible */
  z-index: 0;
}

.heart-element {
  position: absolute;
  pointer-events: none;

  /* Optimize rendering for smooth animations */
  will-change: transform, opacity;

  /* Smooth edges for better visual quality */
  image-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.heart-element svg {
  width: 100%;
  height: 100%;
  display: block;
}

/* Performance optimization for reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .heart-element {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
