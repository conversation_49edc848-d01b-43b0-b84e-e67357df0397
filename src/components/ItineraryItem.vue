<template>
  <li class="text-center">
    <div class="text-xs text-neutral-500 uppercase tracking-wide mb-1">
      {{ time }}
    </div>
    <h3 class="text-base tracking-wider font-semibold text-neutral-800">
      {{ title.toUpperCase() }}
    </h3>
    <p class="text-sm text-neutral-600 leading-relaxed max-w-xl mx-auto">
      {{ description }}
    </p>
  </li>
</template>

<script setup lang="ts">
interface ItineraryItemProps {
  time: string
  title: string
  description: string
}

defineProps<ItineraryItemProps>()
</script>
