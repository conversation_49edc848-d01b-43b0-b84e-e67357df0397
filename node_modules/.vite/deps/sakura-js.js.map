{"version": 3, "sources": ["../../sakura-js/dist/sakura.js"], "sourcesContent": ["/*!\n * Sakura.js 1.1.1\n * Vanilla JS version of jQuery-Sakura: Make it rain sakura petals.\n * https://github.com/jhammann/sakura\n *\n * Copyright 2019-2019 <PERSON><PERSON><PERSON>\n *\n * Released under the MIT License\n *\n * Released on: September 4, 2019\n */\n\"use strict\";\n\nvar Sakura = function Sakura(selector, options) {\n  var _this = this;\n\n  if (typeof selector === 'undefined') {\n    throw new Error('No selector present. Define an element.');\n  }\n\n  this.el = document.querySelector(selector); // Defaults for the option object, which gets extended below.\n\n  var defaults = {\n    className: 'sakura',\n    // Classname of the petal. This corresponds with the css.\n    fallSpeed: 1,\n    // Speed factor in which the petal falls (higher is slower).\n    maxSize: 14,\n    // The maximum size of the petal.\n    minSize: 10,\n    // The minimum size of the petal.\n    delay: 300,\n    // Delay between petals.\n    colors: [{\n      // You can add multiple colors (chosen randomly) by adding elements to the array.\n      gradientColorStart: 'rgba(255, 183, 197, 0.9)',\n      // Gradient color start (rgba).\n      gradientColorEnd: 'rgba(255, 197, 208, 0.9)',\n      // Gradient color end (rgba).\n      gradientColorDegree: 120 // Gradient degree angle.\n\n    }]\n  }; // Merge defaults with user options.\n\n  var extend = function extend(originalObj, newObj) {\n    Object.keys(originalObj).forEach(function (key) {\n      if (newObj && Object.prototype.hasOwnProperty.call(newObj, key)) {\n        var origin = originalObj;\n        origin[key] = newObj[key];\n      }\n    });\n    return originalObj;\n  };\n\n  this.settings = extend(defaults, options); // Hide horizontal scrollbars on the target element.\n\n  this.el.style.overflowX = 'hidden'; // Random array element\n\n  function randomArrayElem(arr) {\n    return arr[Math.floor(Math.random() * arr.length)];\n  } // Random integer\n\n\n  function randomInt(min, max) {\n    return Math.floor(Math.random() * (max - min + 1)) + min;\n  } // Check for animation events.\n\n\n  var prefixes = ['webkit', 'moz', 'MS', 'o', ''];\n\n  function PrefixedEvent(element, type, callback) {\n    for (var p = 0; p < prefixes.length; p += 1) {\n      var animType = type;\n\n      if (!prefixes[p]) {\n        animType = type.toLowerCase();\n      }\n\n      element.addEventListener(prefixes[p] + animType, callback, false);\n    }\n  } // Check if the element is in the viewport.\n\n\n  function elementInViewport(el) {\n    var rect = el.getBoundingClientRect();\n    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && rect.right <= (window.innerWidth || document.documentElement.clientWidth);\n  }\n\n  this.createPetal = function () {\n    if (_this.el.dataset.sakuraAnimId) {\n      setTimeout(function () {\n        window.requestAnimationFrame(_this.createPetal);\n      }, _this.settings.delay);\n    } // Name the animations. These have to match the animations in the CSS file.\n\n\n    var animationNames = {\n      blowAnimations: ['blow-soft-left', 'blow-medium-left', 'blow-soft-right', 'blow-medium-right'],\n      swayAnimations: ['sway-0', 'sway-1', 'sway-2', 'sway-3', 'sway-4', 'sway-5', 'sway-6', 'sway-7', 'sway-8']\n    }; // Get one random animation of each type and randomize fall time of the petals\n\n    var blowAnimation = randomArrayElem(animationNames.blowAnimations);\n    var swayAnimation = randomArrayElem(animationNames.swayAnimations);\n\n    var fallTime = (document.documentElement.clientHeight * 0.007 + Math.round(Math.random() * 5)) * _this.settings.fallSpeed; // Create animations\n\n\n    var animationsArr = [\"fall \".concat(fallTime, \"s linear 0s 1\"), \"\".concat(blowAnimation, \" \").concat((fallTime > 30 ? fallTime : 30) - 20 + randomInt(0, 20), \"s linear 0s infinite\"), \"\".concat(swayAnimation, \" \").concat(randomInt(2, 4), \"s linear 0s infinite\")];\n    var animations = animationsArr.join(', '); // Create petal and give it a random size.\n\n    var petal = document.createElement('div');\n    petal.classList.add(_this.settings.className);\n    var height = randomInt(_this.settings.minSize, _this.settings.maxSize);\n    var width = height - Math.floor(randomInt(0, _this.settings.minSize) / 3); // Get a random color.\n\n    var color = randomArrayElem(_this.settings.colors);\n    petal.style.background = \"linear-gradient(\".concat(color.gradientColorDegree, \"deg, \").concat(color.gradientColorStart, \", \").concat(color.gradientColorEnd, \")\");\n    petal.style.webkitAnimation = animations;\n    petal.style.animation = animations;\n    petal.style.borderRadius = \"\".concat(randomInt(_this.settings.maxSize, _this.settings.maxSize + Math.floor(Math.random() * 10)), \"px \").concat(randomInt(1, Math.floor(width / 4)), \"px\");\n    petal.style.height = \"\".concat(height, \"px\");\n    petal.style.left = \"\".concat(Math.random() * document.documentElement.clientWidth - 100, \"px\");\n    petal.style.marginTop = \"\".concat(-(Math.floor(Math.random() * 20) + 15), \"px\");\n    petal.style.width = \"\".concat(width, \"px\"); // Remove petals of which the animation ended.\n\n    PrefixedEvent(petal, 'AnimationEnd', function () {\n      if (!elementInViewport(petal)) {\n        petal.remove();\n      }\n    }); // Remove petals that float out of the viewport.\n\n    PrefixedEvent(petal, 'AnimationIteration', function () {\n      if (!elementInViewport(petal)) {\n        petal.remove();\n      }\n    }); // Add the petal to the target element.\n\n    _this.el.appendChild(petal);\n  };\n\n  this.el.setAttribute('data-sakura-anim-id', window.requestAnimationFrame(this.createPetal));\n};\n\nSakura.prototype.start = function () {\n  var animId = this.el.dataset.sakuraAnimId;\n\n  if (!animId) {\n    this.el.setAttribute('data-sakura-anim-id', window.requestAnimationFrame(this.createPetal));\n  } else {\n    throw new Error('Sakura is already running.');\n  }\n};\n\nSakura.prototype.stop = function () {\n  var _this2 = this;\n\n  var graceful = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  var animId = this.el.dataset.sakuraAnimId;\n\n  if (animId) {\n    window.cancelAnimationFrame(animId);\n    this.el.setAttribute('data-sakura-anim-id', '');\n  } // Remove all current blossoms at once.\n  // You can also set 'graceful' to true to stop new petals from being created.\n  // This way the petals won't be removed abruptly.\n\n\n  if (!graceful) {\n    setTimeout(function () {\n      var petals = document.getElementsByClassName(_this2.settings.className);\n\n      while (petals.length > 0) {\n        petals[0].parentNode.removeChild(petals[0]);\n      }\n    }, this.settings.delay + 50);\n  }\n};"], "mappings": ";AAaA,IAAI,SAAS,SAASA,QAAO,UAAU,SAAS;AAC9C,MAAI,QAAQ;AAEZ,MAAI,OAAO,aAAa,aAAa;AACnC,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC3D;AAEA,OAAK,KAAK,SAAS,cAAc,QAAQ;AAEzC,MAAI,WAAW;AAAA,IACb,WAAW;AAAA;AAAA,IAEX,WAAW;AAAA;AAAA,IAEX,SAAS;AAAA;AAAA,IAET,SAAS;AAAA;AAAA,IAET,OAAO;AAAA;AAAA,IAEP,QAAQ,CAAC;AAAA;AAAA,MAEP,oBAAoB;AAAA;AAAA,MAEpB,kBAAkB;AAAA;AAAA,MAElB,qBAAqB;AAAA;AAAA,IAEvB,CAAC;AAAA,EACH;AAEA,MAAI,SAAS,SAASC,QAAO,aAAa,QAAQ;AAChD,WAAO,KAAK,WAAW,EAAE,QAAQ,SAAU,KAAK;AAC9C,UAAI,UAAU,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAC/D,YAAI,SAAS;AACb,eAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAEA,OAAK,WAAW,OAAO,UAAU,OAAO;AAExC,OAAK,GAAG,MAAM,YAAY;AAE1B,WAAS,gBAAgB,KAAK;AAC5B,WAAO,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM,CAAC;AAAA,EACnD;AAGA,WAAS,UAAU,KAAK,KAAK;AAC3B,WAAO,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,MAAM,EAAE,IAAI;AAAA,EACvD;AAGA,MAAI,WAAW,CAAC,UAAU,OAAO,MAAM,KAAK,EAAE;AAE9C,WAAS,cAAc,SAAS,MAAM,UAAU;AAC9C,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,UAAI,WAAW;AAEf,UAAI,CAAC,SAAS,CAAC,GAAG;AAChB,mBAAW,KAAK,YAAY;AAAA,MAC9B;AAEA,cAAQ,iBAAiB,SAAS,CAAC,IAAI,UAAU,UAAU,KAAK;AAAA,IAClE;AAAA,EACF;AAGA,WAAS,kBAAkB,IAAI;AAC7B,QAAI,OAAO,GAAG,sBAAsB;AACpC,WAAO,KAAK,OAAO,KAAK,KAAK,QAAQ,KAAK,KAAK,WAAW,OAAO,eAAe,SAAS,gBAAgB,iBAAiB,KAAK,UAAU,OAAO,cAAc,SAAS,gBAAgB;AAAA,EACzL;AAEA,OAAK,cAAc,WAAY;AAC7B,QAAI,MAAM,GAAG,QAAQ,cAAc;AACjC,iBAAW,WAAY;AACrB,eAAO,sBAAsB,MAAM,WAAW;AAAA,MAChD,GAAG,MAAM,SAAS,KAAK;AAAA,IACzB;AAGA,QAAI,iBAAiB;AAAA,MACnB,gBAAgB,CAAC,kBAAkB,oBAAoB,mBAAmB,mBAAmB;AAAA,MAC7F,gBAAgB,CAAC,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,QAAQ;AAAA,IAC3G;AAEA,QAAI,gBAAgB,gBAAgB,eAAe,cAAc;AACjE,QAAI,gBAAgB,gBAAgB,eAAe,cAAc;AAEjE,QAAI,YAAY,SAAS,gBAAgB,eAAe,OAAQ,KAAK,MAAM,KAAK,OAAO,IAAI,CAAC,KAAK,MAAM,SAAS;AAGhH,QAAI,gBAAgB,CAAC,QAAQ,OAAO,UAAU,eAAe,GAAG,GAAG,OAAO,eAAe,GAAG,EAAE,QAAQ,WAAW,KAAK,WAAW,MAAM,KAAK,UAAU,GAAG,EAAE,GAAG,sBAAsB,GAAG,GAAG,OAAO,eAAe,GAAG,EAAE,OAAO,UAAU,GAAG,CAAC,GAAG,sBAAsB,CAAC;AACpQ,QAAI,aAAa,cAAc,KAAK,IAAI;AAExC,QAAI,QAAQ,SAAS,cAAc,KAAK;AACxC,UAAM,UAAU,IAAI,MAAM,SAAS,SAAS;AAC5C,QAAI,SAAS,UAAU,MAAM,SAAS,SAAS,MAAM,SAAS,OAAO;AACrE,QAAI,QAAQ,SAAS,KAAK,MAAM,UAAU,GAAG,MAAM,SAAS,OAAO,IAAI,CAAC;AAExE,QAAI,QAAQ,gBAAgB,MAAM,SAAS,MAAM;AACjD,UAAM,MAAM,aAAa,mBAAmB,OAAO,MAAM,qBAAqB,OAAO,EAAE,OAAO,MAAM,oBAAoB,IAAI,EAAE,OAAO,MAAM,kBAAkB,GAAG;AAChK,UAAM,MAAM,kBAAkB;AAC9B,UAAM,MAAM,YAAY;AACxB,UAAM,MAAM,eAAe,GAAG,OAAO,UAAU,MAAM,SAAS,SAAS,MAAM,SAAS,UAAU,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE,CAAC,GAAG,KAAK,EAAE,OAAO,UAAU,GAAG,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG,IAAI;AACxL,UAAM,MAAM,SAAS,GAAG,OAAO,QAAQ,IAAI;AAC3C,UAAM,MAAM,OAAO,GAAG,OAAO,KAAK,OAAO,IAAI,SAAS,gBAAgB,cAAc,KAAK,IAAI;AAC7F,UAAM,MAAM,YAAY,GAAG,OAAO,EAAE,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE,IAAI,KAAK,IAAI;AAC9E,UAAM,MAAM,QAAQ,GAAG,OAAO,OAAO,IAAI;AAEzC,kBAAc,OAAO,gBAAgB,WAAY;AAC/C,UAAI,CAAC,kBAAkB,KAAK,GAAG;AAC7B,cAAM,OAAO;AAAA,MACf;AAAA,IACF,CAAC;AAED,kBAAc,OAAO,sBAAsB,WAAY;AACrD,UAAI,CAAC,kBAAkB,KAAK,GAAG;AAC7B,cAAM,OAAO;AAAA,MACf;AAAA,IACF,CAAC;AAED,UAAM,GAAG,YAAY,KAAK;AAAA,EAC5B;AAEA,OAAK,GAAG,aAAa,uBAAuB,OAAO,sBAAsB,KAAK,WAAW,CAAC;AAC5F;AAEA,OAAO,UAAU,QAAQ,WAAY;AACnC,MAAI,SAAS,KAAK,GAAG,QAAQ;AAE7B,MAAI,CAAC,QAAQ;AACX,SAAK,GAAG,aAAa,uBAAuB,OAAO,sBAAsB,KAAK,WAAW,CAAC;AAAA,EAC5F,OAAO;AACL,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AACF;AAEA,OAAO,UAAU,OAAO,WAAY;AAClC,MAAI,SAAS;AAEb,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,MAAI,SAAS,KAAK,GAAG,QAAQ;AAE7B,MAAI,QAAQ;AACV,WAAO,qBAAqB,MAAM;AAClC,SAAK,GAAG,aAAa,uBAAuB,EAAE;AAAA,EAChD;AAKA,MAAI,CAAC,UAAU;AACb,eAAW,WAAY;AACrB,UAAI,SAAS,SAAS,uBAAuB,OAAO,SAAS,SAAS;AAEtE,aAAO,OAAO,SAAS,GAAG;AACxB,eAAO,CAAC,EAAE,WAAW,YAAY,OAAO,CAAC,CAAC;AAAA,MAC5C;AAAA,IACF,GAAG,KAAK,SAAS,QAAQ,EAAE;AAAA,EAC7B;AACF;", "names": ["Sakura", "extend"]}